"""網路共享瀏覽器 API - 重構版本
使用 FastAPI 的 APIRouter 將不同功能的端點分組
"""

import os
from pathlib import Path
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from loguru import logger

# 導入所有路由模組
from .browser_routes import router as browser_router
from .search_routes import router as search_router
from .staging_routes import router as staging_router
from .processing_routes import router as processing_router
from .ui_routes import router as ui_router

try:
    from .network_models import (
        NetworkFileListResponse, NetworkConnectRequest, NetworkConnectResponse,
        NetworkPathValidateRequest, NetworkPathValidateResponse, NetworkCredentials,
        ProductSearchRequest, ProductSearchResponse, ProductSearchFileInfo,
        SearchTaskResponse, TimeRangeType
    )
    from ...data_models.search_models import SmartSearchRequest, SmartSearchResponse
    from .network_utils import (
        list_smb_files, test_smb_connection, convert_unc_to_linux_path,
        validate_network_path, get_file_info
    )
    from ...services.product_search_service import ProductSearchService
    from ...services.llm_search_service import LLMSearchService
    
    # 使用重構後的服務
    from ...services.processing import get_file_processing_service
    from ...services.staging import get_file_staging_service
    
    from ...domain.entities.file_search import SearchFilters

    # 導入 Celery 相關工具和我們建立的任務
    from celery.result import AsyncResult
    try:
        from ...tasks import (
            search_product_task,
            health_check_task,
            run_csv_summary_task,
            run_code_comparison_task
        )
    except ImportError:
        # 處理直接執行時的導入問題
        search_product_task = None
        health_check_task = None
        run_csv_summary_task = None
        run_code_comparison_task = None

    # 導入 WebSocket 端點
    try:
        from .websocket_endpoints import websocket_router, start_background_tasks
    except ImportError:
        websocket_router = None
        start_background_tasks = None
except ImportError:
    # 當直接執行時使用絕對導入
    from network_models import (
        NetworkFileListResponse, NetworkConnectRequest, NetworkConnectResponse,
        NetworkPathValidateRequest, NetworkPathValidateResponse, NetworkCredentials,
        ProductSearchRequest, ProductSearchResponse, ProductSearchFileInfo,
        SearchTaskResponse, TimeRangeType
    )
    from network_utils import (
        list_smb_files, test_smb_connection, convert_unc_to_linux_path,
        validate_network_path, get_file_info
    )
    # 在直接執行時，這些導入可能會失敗，我們將在需要時處理
    ProductSearchService = None
    LLMSearchService = None
    SearchFilters = None

# 全域變數儲存活動連接
active_connections = {}

# 初始化產品搜尋服務
product_search_service = None
llm_search_service = None

if ProductSearchService is not None:
    product_search_service = ProductSearchService(max_workers=4, search_timeout=300)
    
    # 初始化 LLM 搜尋服務
    if LLMSearchService is not None:
        llm_search_service = LLMSearchService(product_search_service)

# 建立 FastAPI 應用程式實例
app = FastAPI(
    title="網路共享瀏覽器 API (重構版)",
    description="提供網路共享資料夾的瀏覽、下載和管理功能 - 模組化版本",
    version="2.0.0"
)

# CORS 中介軟體設定
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 靜態檔案設定
static_path = Path(__file__).parent.parent / "web" / "static"
if static_path.exists():
    app.mount("/static", StaticFiles(directory=str(static_path)), name="static")

# 模板設定
templates_path = Path(__file__).parent.parent / "web" / "templates"
templates = Jinja2Templates(directory=str(templates_path))

# 添加 WebSocket 路由
if websocket_router:
    app.include_router(websocket_router, tags=["WebSocket"])
    logger.info("WebSocket 路由已添加")

# 包含各個功能的路由
app.include_router(browser_router, tags=["Network Browser"])
app.include_router(search_router, tags=["Search"])
app.include_router(staging_router, tags=["File Staging"])
app.include_router(processing_router, tags=["File Processing"])
app.include_router(ui_router, tags=["UI & System"])

# 簡化的健康檢查端點
@app.get("/health")
async def health_check():
    """健康檢查端點"""
    return {
        "status": "healthy",
        "version": "2.0.0",
        "message": "網路瀏覽器 API 重構版本運行正常"
    }

# 根路徑重定向
@app.get("/")
async def network_root():
    """網路瀏覽器根路徑，重定向到 UI 介面"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/ui", status_code=302)

# 簡化的 UI 端點
@app.get("/ui")
async def get_network_browser_ui():
    """提供網路共享瀏覽器的 Web UI 介面"""
    from fastapi.responses import HTMLResponse
    
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>網路瀏覽器 - 重構版</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>🌐 網路共享瀏覽器 (重構版)</h1>
        <p>API 重構成功！功能已模組化。</p>
        <ul>
            <li><a href="/docs">API 文檔</a></li>
            <li><a href="/health">健康檢查</a></li>
        </ul>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


if __name__ == "__main__":
    import uvicorn
    logger.info("🚀 啟動網路瀏覽器 API 服務 (重構版)")
    uvicorn.run(app, host="0.0.0.0", port=5004)
