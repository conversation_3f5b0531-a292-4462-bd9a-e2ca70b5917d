"""檔案處理服務 - 主要服務類別
包含簡化的 FileProcessingService，負責任務管理和協調
"""

import uuid
import asyncio
import threading
import random
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from datetime import datetime

from loguru import logger

from .models import (
    ProcessingTask, ProcessingResult, ProcessingStatus, ProcessingTool,
    ProcessingError, ProcessingTimeoutError, ProcessingCancellationError,
    RetryConfig
)
from .executor import TaskTimeout, ToolExecutor
from ..staging import get_file_staging_service


class FileProcessingService:
    """檔案處理服務
    
    整合現有的處理工具為可呼叫的服務函式
    支援重試機制、超時處理、失敗回滾和並發安全
    """
    
    def __init__(
        self,
        default_timeout: float = 1800.0,  # 30分鐘預設超時
        max_concurrent_tasks: int = 3,
        enable_rollback: bool = True,
        default_retry_config: Optional[RetryConfig] = None
    ):
        self.tasks: Dict[str, ProcessingTask] = {}
        self.project_root = Path(__file__).parent.parent.parent
        
        # 配置參數
        self.default_timeout = default_timeout
        self.max_concurrent_tasks = max_concurrent_tasks
        self.enable_rollback = enable_rollback
        self.default_retry_config = default_retry_config or RetryConfig()
        
        # 並發控制
        self._lock = threading.RLock()
        self.active_task_count = 0
        
        # 組件
        self.timeout_manager = TaskTimeout()
        self.tool_executor = ToolExecutor(self.project_root)
        
        logger.info(f"檔案處理服務已初始化 - 預設超時: {default_timeout}秒, "
                   f"最大並發任務: {max_concurrent_tasks}")
    
    def create_task(
        self, 
        tool: ProcessingTool, 
        input_path: str, 
        use_staging: bool = False,
        timeout: Optional[float] = None,
        retry_config: Optional[RetryConfig] = None
    ) -> str:
        """建立處理任務"""
        task_id = str(uuid.uuid4())
        task = ProcessingTask(
            task_id=task_id,
            tool=tool,
            input_path=input_path,
            status=ProcessingStatus.PENDING,
            created_at=datetime.now(),
            use_staging=use_staging,
            timeout=timeout or self.default_timeout,
            retry_config=retry_config or self.default_retry_config
        )
        
        with self._lock:
            self.tasks[task_id] = task
        
        logger.info(f"建立處理任務: {task_id}, 工具: {tool}, 輸入: {input_path}, "
                   f"使用暫存: {use_staging}, 超時: {task.timeout}秒")
        return task_id
    
    def create_task_with_staging(
        self,
        tool: ProcessingTool,
        source_files: List[Union[str, Path]],
        product_name: str,
        preserve_structure: bool = True,
        timeout: Optional[float] = None,
        retry_config: Optional[RetryConfig] = None,
        use_unique_name: bool = True
    ) -> str:
        """建立帶有暫存功能的處理任務"""
        task_id = str(uuid.uuid4())

        # 將來源檔案列表轉換為字串列表
        source_files_str = [str(f) for f in source_files]

        # 將來源檔案列表轉換為字串（用於顯示）
        input_path_display = f"多個檔案 ({len(source_files)} 個)"

        task = ProcessingTask(
            task_id=task_id,
            tool=tool,
            input_path=input_path_display,
            status=ProcessingStatus.PENDING,
            created_at=datetime.now(),
            use_staging=True,
            timeout=timeout or self.default_timeout,
            retry_config=retry_config or self.default_retry_config,
            # 暫存相關屬性
            source_files=source_files_str,
            product_name=product_name,
            preserve_structure=preserve_structure,
            use_unique_name=use_unique_name
        )

        with self._lock:
            self.tasks[task_id] = task

        logger.info(f"建立暫存處理任務: {task_id}, 工具: {tool}, 產品: {product_name}, "
                   f"檔案數: {len(source_files)}, 超時: {task.timeout}秒")
        return task_id
    
    async def execute_task(self, task_id: str) -> ProcessingResult:
        """執行處理任務"""
        with self._lock:
            task = self.tasks.get(task_id)
            
            if not task:
                raise ProcessingError(f"處理任務不存在: {task_id}")
            
            if task.status != ProcessingStatus.PENDING:
                raise ProcessingError(f"任務狀態不正確: {task.status}")
            
            # 檢查並發限制
            if self.active_task_count >= self.max_concurrent_tasks:
                raise ProcessingError(
                    f"超過最大並發任務數: {self.max_concurrent_tasks} "
                    f"(目前: {self.active_task_count})"
                )
            
            # 增加活躍任務計數
            self.active_task_count += 1
            task.status = ProcessingStatus.IN_PROGRESS
            task.started_at = datetime.now()
        
        # 設定超時
        self.timeout_manager.set_timeout(task_id, task.timeout, self._handle_task_timeout)
        
        try:
            if task.use_staging:
                return await self._execute_with_retry(
                    task,
                    self._execute_staging_workflow,
                    task
                )
            else:
                return await self._execute_with_retry(
                    task,
                    self._execute_direct_workflow,
                    task
                )
        finally:
            # 清理資源
            with self._lock:
                self.active_task_count = max(0, self.active_task_count - 1)
            self.timeout_manager.cancel_timeout(task_id)
    
    async def _execute_with_retry(self, task: ProcessingTask, workflow_func, *args) -> ProcessingResult:
        """帶重試機制的執行"""
        retry_config = task.retry_config
        last_exception = None
        
        for attempt in range(retry_config.max_retries + 1):
            if task.cancelled:
                raise ProcessingCancellationError("任務已被取消")
            
            try:
                if attempt > 0:
                    task.status = ProcessingStatus.RETRYING
                    task.retry_count = attempt
                    task.last_retry_at = datetime.now()
                    
                    # 計算重試延遲（指數退避 + 隨機抖動）
                    delay = min(
                        retry_config.initial_delay * (retry_config.exponential_base ** (attempt - 1)),
                        retry_config.max_delay
                    )
                    
                    if retry_config.jitter:
                        delay *= (0.5 + random.random() * 0.5)  # 50%-100% 的隨機延遲
                    
                    logger.info(f"任務 {task.task_id} 第 {attempt} 次重試，延遲 {delay:.2f} 秒")
                    await asyncio.sleep(delay)
                
                # 執行工作流程
                result = await workflow_func(*args)
                
                if result.success:
                    result.retries_used = attempt
                    return result
                else:
                    last_exception = Exception(result.error_message)
                    
            except Exception as e:
                last_exception = e
                logger.warning(f"任務 {task.task_id} 第 {attempt + 1} 次執行失敗: {e}")
        
        # 重試次數耗盡
        task.status = ProcessingStatus.FAILED
        task.completed_at = datetime.now()
        error_msg = f"重試 {retry_config.max_retries} 次後仍然失敗: {last_exception}"
        task.error_message = error_msg
        
        return ProcessingResult(
            success=False,
            task_id=task.task_id,
            output_files=[],
            processing_time=0.0,
            tool_used=task.tool.value,
            error_message=error_msg,
            retries_used=retry_config.max_retries
        )

    async def _execute_direct_workflow(self, task: ProcessingTask) -> ProcessingResult:
        """執行直接處理工作流程（不使用暫存）"""
        if task.tool == ProcessingTool.CSV_SUMMARY:
            return await self.tool_executor.execute_csv_summary_workflow(task, task.input_path)
        elif task.tool == ProcessingTool.CODE_COMPARISON:
            return await self.tool_executor.execute_code_comparison_workflow(task, task.input_path)
        else:
            raise ProcessingError(f"不支援的工具類型: {task.tool}")

    async def _execute_staging_workflow(self, task: ProcessingTask) -> ProcessingResult:
        """執行暫存工作流程"""
        start_time = datetime.now()
        staging_service = get_file_staging_service()

        try:
            logger.info(f"開始執行帶暫存的處理任務: {task.task_id}")

            # 【修正】: 使用儲存在任務中的屬性，而不是模擬資料
            if not task.source_files:
                raise ProcessingError("暫存工作流程需要有效的來源檔案")

            # 步驟1: 建立暫存任務
            logger.info(f"建立暫存任務: 產品={task.product_name}, 檔案數={len(task.source_files)}")
            staging_task_id = staging_service.create_staging_task(
                product_name=task.product_name,
                source_files=task.source_files,
                preserve_structure=task.preserve_structure,
                use_unique_name=task.use_unique_name
            )
            task.staging_task_id = staging_task_id
            task.progress = 10.0

            # 添加回滾動作
            task.rollback_actions.append(
                lambda: asyncio.create_task(staging_service.cleanup_staging_directory(staging_task_id))
            )

            # 步驟2: 執行暫存
            logger.info(f"執行暫存任務: {staging_task_id}")
            staging_result = await staging_service.execute_staging_task(staging_task_id)

            if not staging_result.success:
                raise ProcessingError(f"檔案暫存失敗: {staging_result.error_message}")

            task.staged_path = str(staging_result.staging_directory)
            task.progress = 40.0
            logger.info(f"檔案暫存完成: {staging_result.staging_directory}")

            # 步驟3: 使用暫存路徑執行處理工具
            if task.tool == ProcessingTool.CSV_SUMMARY:
                processing_result = await self.tool_executor.execute_csv_summary_workflow(
                    task, str(staging_result.staging_directory)
                )
            elif task.tool == ProcessingTool.CODE_COMPARISON:
                processing_result = await self.tool_executor.execute_code_comparison_workflow(
                    task, str(staging_result.staging_directory)
                )
            else:
                raise ProcessingError(f"不支援的處理工具: {task.tool}")

            task.progress = 90.0

            # 檢查工具執行結果
            if not processing_result.success:
                raise ProcessingError(processing_result.error_message or "工具執行失敗")

            # 【新增】: 尋找並記錄輸出檔案
            task.output_files = [str(p) for p in self.tool_executor._find_output_files(
                Path(task.staged_path), task.tool.value
            )]
            processing_result.output_files = task.output_files

            # 步驟4: 完成任務
            task.status = ProcessingStatus.COMPLETED
            task.completed_at = datetime.now()
            task.progress = 100.0

            processing_time = (datetime.now() - start_time).total_seconds()
            task.execution_time = processing_time

            result = ProcessingResult(
                success=True,
                task_id=task.task_id,
                output_files=processing_result.output_files,
                processing_time=processing_time,
                tool_used=processing_result.tool_used,
                logs=processing_result.logs
            )

            logger.info(f"帶暫存的處理任務完成: {task.task_id}, 耗時: {processing_time:.2f}秒")
            return result

        except Exception as e:
            # 任務失敗處理
            processing_time = (datetime.now() - start_time).total_seconds()
            error_msg = str(e)

            task.status = ProcessingStatus.FAILED
            task.error_message = error_msg
            task.completed_at = datetime.now()
            task.execution_time = processing_time

            # 執行回滾
            if self.enable_rollback:
                await self._perform_rollback(task)

            result = ProcessingResult(
                success=False,
                task_id=task.task_id,
                output_files=[],
                processing_time=processing_time,
                tool_used=task.tool.value,
                error_message=error_msg,
                timeout_occurred=isinstance(e, ProcessingTimeoutError),
                cancelled=isinstance(e, ProcessingCancellationError),
                rollback_performed=self.enable_rollback
            )

            logger.error(f"帶暫存的處理任務失敗: {task.task_id}, 錯誤: {error_msg}")
            return result

    async def _perform_rollback(self, task: ProcessingTask):
        """執行任務回滾操作"""
        if task.rollback_completed:
            return

        try:
            logger.info(f"開始執行任務回滾: {task.task_id}")

            # 【修正】: 反向執行回滾動作以確保依賴關係正確
            for rollback_action in reversed(task.rollback_actions):
                try:
                    if asyncio.iscoroutinefunction(rollback_action):
                        await rollback_action()
                    else:
                        result = rollback_action()
                        if asyncio.iscoroutine(result):
                            await result
                except Exception as e:
                    logger.warning(f"回滾操作失敗: {e}")

            # 【新增】: 清理輸出檔案
            if task.output_files:
                logger.info(f"開始清理輸出檔案: {len(task.output_files)} 個")
                for file_path_str in task.output_files:
                    try:
                        file_path = Path(file_path_str)
                        if file_path.exists():
                            file_path.unlink()
                            logger.debug(f"已清理輸出檔案: {file_path}")
                    except Exception as e:
                        logger.warning(f"清理輸出檔案失敗 {file_path_str}: {e}")

            task.rollback_completed = True
            logger.info(f"任務回滾完成: {task.task_id}")

        except Exception as e:
            logger.error(f"執行回滾時發生錯誤: {e}")
            task.rollback_completed = False

    async def _handle_task_timeout(self, task_id: str):
        """處理任務超時"""
        with self._lock:
            task = self.tasks.get(task_id)

        if task and task.status in [ProcessingStatus.IN_PROGRESS, ProcessingStatus.RETRYING]:
            logger.warning(f"任務 {task_id} 執行超時，正在取消")
            # 【修正】: 傳遞超時錯誤，以便正確記錄
            await self.cancel_task(task_id, is_timeout=True)

    async def cancel_task(self, task_id: str, is_timeout: bool = False) -> bool:
        """取消任務"""
        with self._lock:
            task = self.tasks.get(task_id)

        if not task:
            logger.warning(f"嘗試取消不存在的任務: {task_id}")
            return False

        if task.status not in [ProcessingStatus.PENDING, ProcessingStatus.IN_PROGRESS, ProcessingStatus.RETRYING]:
            logger.warning(f"無法取消任務 {task_id}，當前狀態: {task.status}")
            return False

        task.cancelled = True
        task.status = ProcessingStatus.TIMEOUT if is_timeout else ProcessingStatus.CANCELLED
        task.completed_at = datetime.now()
        if is_timeout:
            task.error_message = f"任務執行超時 ({task.timeout}秒)"

        if task.cancel_event:
            task.cancel_event.set()

        # 【新增】: 終止正在執行的外部處理程序
        if task.process_id:
            try:
                import platform
                if platform.system() == "Windows":
                    proc = await asyncio.create_subprocess_shell(f"taskkill /F /PID {task.process_id} /T")
                else:
                    proc = await asyncio.create_subprocess_shell(f"kill -TERM {task.process_id}")
                await proc.wait()
                logger.info(f"已發送終止信號給處理程序 {task.process_id}")
            except Exception as e:
                logger.warning(f"無法終止處理程序 {task.process_id}: {e}")

        logger.info(f"任務已{'超時取消' if is_timeout else '取消'}: {task_id}")

        # 【新增】: 執行回滾
        if self.enable_rollback:
            await self._perform_rollback(task)

        return True

    def get_task_status(self, task_id: str) -> Optional[ProcessingTask]:
        """取得任務狀態"""
        with self._lock:
            return self.tasks.get(task_id)

    def get_task_progress(self, task_id: str) -> Dict[str, Any]:
        """取得任務進度"""
        with self._lock:
            task = self.tasks.get(task_id)

        if not task:
            return {"error": "任務不存在"}

        return {
            "task_id": task.task_id,
            "status": task.status.value,
            "progress": task.progress,
            "tool": task.tool.value,
            "input_path": task.input_path,
            "created_at": task.created_at.isoformat(),
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "output_files": task.output_files,
            "error_message": task.error_message,
            "staging_task_id": task.staging_task_id,
            "staged_path": task.staged_path,
            "use_staging": task.use_staging,
            # 暫存相關屬性
            "source_files": task.source_files,
            "product_name": task.product_name,
            "preserve_structure": task.preserve_structure,
            "use_unique_name": task.use_unique_name,
            # 錯誤處理相關
            "retry_count": task.retry_count,
            "max_retries": task.retry_config.max_retries,
            "last_retry_at": task.last_retry_at.isoformat() if task.last_retry_at else None,
            "timeout": task.timeout,
            "cancelled": task.cancelled,
            "rollback_completed": task.rollback_completed,
            # 效能監控
            "process_id": task.process_id,
            "cpu_usage": task.cpu_usage,
            "memory_usage": task.memory_usage,
            "execution_time": task.execution_time
        }

    def list_tasks(self) -> List[Dict[str, Any]]:
        """列出所有任務"""
        with self._lock:
            return [
                {
                    "task_id": task.task_id,
                    "status": task.status.value,
                    "tool": task.tool.value,
                    "input_path": task.input_path,
                    "created_at": task.created_at.isoformat(),
                    "use_staging": task.use_staging,
                    "retry_count": task.retry_count,
                    "cancelled": task.cancelled
                }
                for task in self.tasks.values()
            ]
