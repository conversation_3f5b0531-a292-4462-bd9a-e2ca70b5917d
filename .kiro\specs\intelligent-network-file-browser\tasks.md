# 實作計畫

## 現狀分析
基於現有程式碼分析，以下功能已實作：
- ✅ 基本網路檔案瀏覽 (network_browser_api.py)
- ✅ SMB/CIFS 連線和驗證 (network_utils.py)
- ✅ 檔案下載功能
- ✅ 基本 UI 介面
- ✅ 處理工具存在 (csv_to_summary.py, code_comparison.py)
- ✅ 產品搜尋服務 (ProductSearchService)
- ✅ LLM 智慧搜尋服務 (LLMSearchService)
- ✅ 檔案處理服務 (FileProcessingService)
- ✅ 背景任務系統 (Celery + Redis)
- ✅ WebSocket 即時通訊
- ✅ 多種 API 端點

## 已完成和待完成功能

- [x] 1. 基本網路檔案系統存取

  - 已實作 SMB/CIFS 協定存取和網路驗證功能
  - 已實作目錄列表、檔案下載功能
  - 已整合 .env 檔案憑證讀取
  - _需求: 1, 7_

- [-] 2. 實作智慧產品搜尋服務







  - 建立 ProductSearchService 類別，支援產品資料夾定位
  - 實作基於產品名稱的資料夾搜尋邏輯
  - 實作時間範圍篩選功能（最近 6 個月等）
  - 實作並行搜尋以提升效能
  - 新增 /network/api/search/product 端點
  - _需求: 2_


- [x] 3. 整合 LLM 智慧搜尋功能
  - 實作 LLMSearchService 類別，整合現有 Ollama 基礎設施
  - 實作自然語言查詢解析功能
  - 實作時間相關查詢解析（「最近 6 個月」、「本季」等）
  - 更新現有的 /network/api/smart-search 端點實作
  - 實作搜尋結果智慧分析和建議
  - _需求: 3_

- [x] 4. 建立檔案處理服務整合

  - 建立 FileProcessingService 類別
  - 整合 csv_to_summary.py 為可呼叫的服務函式
  - 整合 code_comparison.py 為可呼叫的服務函式
  - 新增 /network/api/process/csv-summary 端點
  - 新增 /network/api/process/code-comparison 端點
  - 實作處理進度追蹤和狀態回報
  - _需求: 4_

- [x] 5. 實作自動檔案暫存功能





  - 實作檔案暫存到 d:\temp\[產品名稱] 的邏輯
  - 建立暫存目錄管理和清理機制
  - 實作檔案完整性驗證
  - 整合到處理服務工作流程中
  - 實作暫存失敗的錯誤處理
  - _需求: 5_

- [ ] 6. 實作自動結果上傳功能
  - 擴展現有 network_utils.py 的上傳功能
  - 實作結果上傳到 \\192.168.1.60\temp_7days\[產品名稱]
  - 實作上傳重試機制（最多 3 次）
  - 實作上傳完成後的暫存檔案清理
  - 整合到處理服務工作流程中
  - _需求: 6_

- [x] 7. 擴展網路瀏覽器 UI 功能
  - 修改 network_browser.html，新增產品搜尋表單
  - 新增時間範圍選擇器（最近 6 個月、本季等）
  - 實作 LLM 自然語言搜尋輸入框
  - 新增「產生摘要」和「比較程式碼」按鈕到檔案操作區
  - 實作檔案選擇功能（支援多選）
  - 新增處理進度指示器和即時狀態更新
  - _需求: 2, 3, 4, 8_

- [ ] 8. 實作錯誤處理和重試機制
  - 建立檔案處理相關的例外類別
  - 實作網路操作自動重試邏輯（已部分實作，需擴展）
  - 實作處理工具執行的錯誤恢復
  - 實作使用者友善的錯誤訊息顯示
  - 新增詳細的錯誤日誌記錄
  - _需求: 7_

- [ ] 9. 建立領域實體和資料模型
  - 建立 src/domain/entities/file_search.py
  - 定義 ProductSearchResult、ProcessingTask 等實體
  - 建立 src/data_models/ 中的 DTO 類別
  - 實作資料驗證和序列化
  - _需求: 1, 2, 3, 4_

- [ ] 10. 效能最佳化和快取
  - 實作搜尋結果快取機制
  - 實作並行檔案操作優化
  - 實作大量搜尋結果的分頁載入
  - 驗證搜尋回應時間 < 5 秒的需求
  - _需求: 1, 2_

- [ ] 11. 安全性增強
  - 實作路徑驗證，防止路徑遍歷攻擊
  - 實作檔案類型和大小限制檢查
  - 確保憑證不在日誌中暴露（已部分實作）
  - 實作暫存檔案自動清理機制
  - _需求: 7_

- [ ] 12. 整合測試和驗證
  - 撰寫完整搜尋到處理工作流程的端對端測試
  - 驗證所有需求的實作完整性
  - 測試多使用者並行存取場景
  - 驗證處理工具整合的正確性
  - _需求: 1, 2, 3, 4, 5, 6, 7, 8_

- [x] 13. 背景任務系統實作
  - 整合 Celery 和 Redis 作為任務佇列系統
  - 實作搜尋和處理的背景任務
  - 建立任務進度追蹤機制
  - 實作任務狀態管理和清理
  - 新增健康檢查任務
  - _需求: 9_

- [x] 14. WebSocket 即時通訊
  - 實作 WebSocket 端點用於即時狀態更新
  - 建立多客戶端連線管理
  - 實作任務狀態廣播機制
  - 新增連線恢復和錯誤處理
  - _需求: 10_

- [x] 15. 搜尋目錄選擇功能
  - 實作多種搜尋目錄選項（自動、全部、特定廠商）
  - 新增廠商目錄智慧識別邏輯
  - 實作階段性搜尋策略（根目錄 → ETD/FT → 其他廠商目錄）
  - 優化搜尋效能和準確性
  - _需求: 11_

- [x] 16. LLM 後備機制
  - 實作 LLM 服務可用性檢查
  - 建立規則式查詢解析作為後備方案
  - 實作自動切換機制
  - 新增服務狀態日誌記錄
  - _需求: 12_

- [x] 17. API 端點擴展
  - 新增產品搜尋 API (/api/search/product)
  - 新增智慧搜尋 API (/api/smart-search)
  - 新增檔案處理 API (/api/process/csv-summary, /api/process/code-comparison)
  - 新增任務管理 API (/api/search/task/{task_id}, /api/process/task/{task_id})
  - 實作完整的請求/回應模型
  - _需求: 2, 3, 4, 9_

- [x] 18. 資料模型和實體
  - 建立完整的領域實體 (FileInfo, ProductSearchResult, ProcessingTask 等)
  - 實作搜尋篩選和時間範圍模型
  - 新增處理狀態和任務管理模型
  - 建立 API 請求/回應的資料傳輸物件
  - _需求: 1, 2, 3, 4, 9_

## 實作優先順序

### 已完成 (高優先級)
- ✅ 任務 1, 2, 3, 4, 7: 核心搜尋和處理功能
- ✅ 任務 13, 14, 15, 16, 17, 18: 進階功能和基礎設施

### 進行中 (中優先級)  
- 🔄 任務 5, 6: 檔案暫存和結果上傳功能

### 待完成 (低優先級)
- ⏳ 任務 8-12: 錯誤處理、最佳化、測試和驗證