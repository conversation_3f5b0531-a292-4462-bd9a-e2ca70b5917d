"""網路瀏覽器 API - 處理相關路由
處理檔案處理任務的建立、執行、狀態查詢功能
"""

from datetime import datetime
from typing import List
from fastapi import APIRouter, Query, HTTPException
from fastapi.responses import JSONResponse

from loguru import logger

try:
    from ...services.processing import get_file_processing_service, ProcessingTool
    from ...services.processing.models import ProcessingError
    
    # 導入 Celery 相關任務
    try:
        from ...tasks import run_csv_summary_task, run_code_comparison_task
    except ImportError:
        run_csv_summary_task = None
        run_code_comparison_task = None
        
except ImportError:
    # 當直接執行時的回退
    get_file_processing_service = None
    ProcessingTool = None
    ProcessingError = Exception
    run_csv_summary_task = None
    run_code_comparison_task = None

# 建立路由器
router = APIRouter(prefix="/api/process", tags=["File Processing"])


@router.post("/csv-summary-with-staging")
async def process_csv_summary_with_staging(
    product_name: str = Query(..., description="產品名稱"),
    source_files: List[str] = Query(..., description="來源檔案路徑列表"),
    preserve_structure: bool = Query(default=True, description="是否保持目錄結構"),
    use_unique_name: bool = Query(default=True, description="是否使用唯一產品名稱")
):
    """帶暫存的 CSV 摘要處理"""
    try:
        if get_file_processing_service is None or ProcessingTool is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        logger.info(f"建立帶暫存的 CSV 摘要處理任務: 產品={product_name}, 檔案數={len(source_files)}")
        
        # 驗證輸入參數
        if not product_name.strip():
            raise HTTPException(status_code=400, detail="產品名稱不能為空")
        
        if not source_files:
            raise HTTPException(status_code=400, detail="來源檔案列表不能為空")
        
        # 取得處理服務
        processing_service = get_file_processing_service()
        
        # 建立帶暫存的處理任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CSV_SUMMARY,
            source_files=source_files,
            product_name=product_name,
            preserve_structure=preserve_structure,
            use_unique_name=use_unique_name
        )
        
        logger.info(f"帶暫存的 CSV 摘要處理任務建立成功: {task_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "帶暫存的 CSV 摘要處理任務建立成功",
            "tool": "csv_summary",
            "product_name": product_name,
            "source_files_count": len(source_files),
            "use_staging": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"帶暫存的 CSV 摘要處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"處理時發生錯誤: {str(e)}")


@router.post("/code-comparison-with-staging")
async def process_code_comparison_with_staging(
    product_name: str = Query(..., description="產品名稱"),
    source_files: List[str] = Query(..., description="來源檔案路徑列表"),
    preserve_structure: bool = Query(default=True, description="是否保持目錄結構"),
    use_unique_name: bool = Query(default=True, description="是否使用唯一產品名稱")
):
    """帶暫存的程式碼比較處理"""
    try:
        if get_file_processing_service is None or ProcessingTool is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        logger.info(f"建立帶暫存的程式碼比較處理任務: 產品={product_name}, 檔案數={len(source_files)}")
        
        # 驗證輸入參數
        if not product_name.strip():
            raise HTTPException(status_code=400, detail="產品名稱不能為空")
        
        if not source_files:
            raise HTTPException(status_code=400, detail="來源檔案列表不能為空")
        
        # 取得處理服務
        processing_service = get_file_processing_service()
        
        # 建立帶暫存的處理任務
        task_id = processing_service.create_task_with_staging(
            tool=ProcessingTool.CODE_COMPARISON,
            source_files=source_files,
            product_name=product_name,
            preserve_structure=preserve_structure,
            use_unique_name=use_unique_name
        )
        
        logger.info(f"帶暫存的程式碼比較處理任務建立成功: {task_id}")
        
        return {
            "success": True,
            "task_id": task_id,
            "message": "帶暫存的程式碼比較處理任務建立成功",
            "tool": "code_comparison",
            "product_name": product_name,
            "source_files_count": len(source_files),
            "use_staging": True
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"帶暫存的程式碼比較處理失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"處理時發生錯誤: {str(e)}")


@router.post("/csv-summary")
async def process_csv_summary_submit(
    input_path: str = Query(..., description="輸入檔案或目錄路徑")
):
    """提交 CSV 摘要生成背景任務"""
    try:
        if run_csv_summary_task is None:
            raise HTTPException(status_code=500, detail="CSV 摘要任務服務未可用")
        
        logger.info(f"提交 CSV 摘要背景任務: {input_path}")
        
        # 提交背景任務
        task = run_csv_summary_task.delay(input_path)
        
        logger.info(f"✅ CSV 摘要任務已提交: {task.id}")
        
        return {
            "success": True,
            "task_id": task.id,
            "message": f"CSV 摘要任務已提交，任務ID: {task.id}",
            "input_path": input_path
        }
        
    except Exception as e:
        logger.error(f"❌ 提交 CSV 摘要任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交 CSV 摘要任務時發生錯誤: {str(e)}")


@router.post("/code-comparison")
async def process_code_comparison_submit(
    input_path: str = Query(..., description="輸入檔案或目錄路徑")
):
    """提交程式碼比較背景任務"""
    try:
        if run_code_comparison_task is None:
            raise HTTPException(status_code=500, detail="程式碼比較任務服務未可用")
        
        logger.info(f"提交程式碼比較背景任務: {input_path}")
        
        # 提交背景任務
        task = run_code_comparison_task.delay(input_path)
        
        logger.info(f"✅ 程式碼比較任務已提交: {task.id}")
        
        return {
            "success": True,
            "task_id": task.id,
            "message": f"程式碼比較任務已提交，任務ID: {task.id}",
            "input_path": input_path
        }
        
    except Exception as e:
        logger.error(f"❌ 提交程式碼比較任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"提交程式碼比較任務時發生錯誤: {str(e)}")


@router.get("/task/{task_id}")
async def get_processing_task_status(task_id: str):
    """獲取處理任務狀態"""
    try:
        if get_file_processing_service is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        file_processing_service = get_file_processing_service()
        
        # 取得任務進度
        progress = file_processing_service.get_task_progress(task_id)
        
        if "error" in progress:
            raise HTTPException(status_code=404, detail=progress["error"])
        
        return progress
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"[ERROR] 獲取處理任務狀態失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"獲取任務狀態時發生錯誤: {str(e)}")


@router.get("/tasks")
async def list_processing_tasks():
    """列出所有處理任務"""
    try:
        if get_file_processing_service is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        file_processing_service = get_file_processing_service()
        tasks = file_processing_service.list_tasks()
        
        return {
            "success": True,
            "tasks": tasks,
            "total_count": len(tasks),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"[ERROR] 列出處理任務失敗: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "success": False,
                "error": f"列出處理任務時發生錯誤: {str(e)}",
                "tasks": [],
                "total_count": 0,
                "timestamp": datetime.now().isoformat()
            }
        )


@router.post("/execute/{task_id}")
async def execute_processing_task(task_id: str):
    """執行處理任務"""
    try:
        if get_file_processing_service is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        logger.info(f"執行處理任務: {task_id}")
        
        # 取得處理服務
        processing_service = get_file_processing_service()
        
        # 檢查任務是否存在
        task = processing_service.get_task_status(task_id)
        if task is None:
            raise HTTPException(status_code=404, detail=f"找不到任務: {task_id}")
        
        # 執行處理任務
        result = await processing_service.execute_task(task_id)
        
        if result.success:
            logger.info(f"處理任務執行成功: {task_id}")
            return {
                "success": True,
                "task_id": task_id,
                "message": "處理任務執行成功",
                "output_files": result.output_files,
                "processing_time": result.processing_time,
                "tool_used": result.tool_used,
                "retries_used": result.retries_used
            }
        else:
            logger.error(f"處理任務執行失敗: {task_id}, 錯誤: {result.error_message}")
            return JSONResponse(
                status_code=422,
                content={
                    "success": False,
                    "task_id": task_id,
                    "message": "處理任務執行失敗",
                    "error_message": result.error_message,
                    "timeout_occurred": result.timeout_occurred,
                    "cancelled": result.cancelled,
                    "rollback_performed": result.rollback_performed
                }
            )
        
    except HTTPException:
        raise
    except ProcessingError as e:
        logger.error(f"處理任務執行失敗 (業務邏輯錯誤): {str(e)}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logger.error(f"執行處理任務時發生錯誤: {str(e)}")
        raise HTTPException(status_code=500, detail=f"執行處理任務時發生錯誤: {str(e)}")


@router.post("/cancel/{task_id}")
async def cancel_processing_task(task_id: str):
    """取消處理任務"""
    try:
        if get_file_processing_service is None:
            raise HTTPException(status_code=500, detail="檔案處理服務未可用")
        
        processing_service = get_file_processing_service()
        success = await processing_service.cancel_task(task_id)
        
        if success:
            return {
                "success": True,
                "task_id": task_id,
                "message": "處理任務已取消"
            }
        else:
            return JSONResponse(
                status_code=400,
                content={
                    "success": False,
                    "task_id": task_id,
                    "message": "無法取消任務（任務可能不存在或已完成）"
                }
            )
        
    except Exception as e:
        logger.error(f"取消處理任務失敗: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任務時發生錯誤: {str(e)}")
