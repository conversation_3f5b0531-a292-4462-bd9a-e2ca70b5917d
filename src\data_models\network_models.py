"""網路相關的資料傳輸物件 (DTOs)"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class NetworkConnectionStatus(str, Enum):
    """網路連線狀態"""
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    ERROR = "error"


class NetworkCredentialsRequest(BaseModel):
    """網路憑證請求模型"""
    username: str = Field(..., description="使用者名稱", min_length=1)
    password: str = Field(..., description="密碼", min_length=1)
    domain: Optional[str] = Field(None, description="網域名稱")
    server: Optional[str] = Field(None, description="伺服器名稱")
    share: Optional[str] = Field(None, description="共享名稱")


class NetworkCredentialsResponse(BaseModel):
    """網路憑證回應模型"""
    status: str
    credentials: Optional[Dict[str, str]] = None
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkConnectRequest(BaseModel):
    """網路連線請求模型"""
    path: str = Field(..., description="網路路徑 (UNC格式)", min_length=1)
    username: str = Field(..., description="使用者名稱", min_length=1)
    password: str = Field(..., description="密碼", min_length=1)
    domain: Optional[str] = Field("gmt", description="網域名稱")


class NetworkConnectResponse(BaseModel):
    """網路連線回應模型"""
    status: str
    connected: bool
    message: str
    mount_point: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkPathValidateRequest(BaseModel):
    """網路路徑驗證請求模型"""
    path: str = Field(..., description="要驗證的網路路徑", min_length=1)


class NetworkPathValidateResponse(BaseModel):
    """網路路徑驗證回應模型"""
    status: str
    valid: bool
    path: str
    message: str
    accessible: bool
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkFileInfo(BaseModel):
    """網路檔案資訊模型"""
    filename: str
    size: int
    size_mb: float
    modified_time: str
    file_type: str
    is_directory: bool
    path: Optional[str] = None


class NetworkFileListRequest(BaseModel):
    """網路檔案列表請求模型"""
    path: str = Field(..., description="網路資料夾路徑", min_length=1)
    recursive: bool = Field(default=False, description="是否遞迴列出子目錄")
    include_hidden: bool = Field(default=False, description="是否包含隱藏檔案")


class NetworkFileListResponse(BaseModel):
    """網路檔案列表回應模型"""
    status: str
    path: str
    files: List[NetworkFileInfo]
    total_count: int
    total_size_mb: float
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkDownloadRequest(BaseModel):
    """網路檔案下載請求模型"""
    path: str = Field(..., description="網路檔案路徑", min_length=1)
    filename: str = Field(..., description="檔案名稱", min_length=1)
    local_path: Optional[str] = Field(None, description="本地儲存路徑")


class NetworkDownloadResponse(BaseModel):
    """網路檔案下載回應模型"""
    status: str
    filename: str
    size: int
    download_path: Optional[str] = None
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkUploadRequest(BaseModel):
    """網路檔案上傳請求模型"""
    local_files: List[str] = Field(..., description="本地檔案列表", min_items=1)
    remote_path: str = Field(..., description="遠端目標路徑", min_length=1)
    product_name: str = Field(..., description="產品名稱", min_length=1)
    overwrite: bool = Field(default=False, description="是否覆蓋現有檔案")


class NetworkUploadResponse(BaseModel):
    """網路檔案上傳回應模型"""
    status: str
    uploaded_files: List[str]
    failed_files: List[str] = []
    total_files: int
    total_size: int
    upload_duration: float
    remote_path: str
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkConfigRequest(BaseModel):
    """網路配置請求模型"""
    base_path: Optional[str] = Field(None, description="基礎網路路徑")
    upload_path: Optional[str] = Field(None, description="上傳路徑")
    staging_path: Optional[str] = Field(None, description="暫存路徑")
    credentials: Optional[NetworkCredentialsRequest] = None


class NetworkConfigResponse(BaseModel):
    """網路配置回應模型"""
    status: str
    config: Dict[str, Any]
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class NetworkHealthCheckResponse(BaseModel):
    """網路健康檢查回應模型"""
    status: str
    connections: Dict[str, NetworkConnectionStatus]
    response_times: Dict[str, float] = {}
    error_messages: Dict[str, str] = {}
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class CurrentUserResponse(BaseModel):
    """當前用戶回應模型"""
    status: str
    user_info: Dict[str, str]
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())