---
inclusion: always
---

# Product Overview - Semiconductor Email Processing System

## System Purpose
Automated email processing system for semiconductor test data from multiple vendors. Migrated from VBA Excel to Python with hexagonal architecture.

## Core Business Logic
- **Email Processing**: Monitor and process vendor emails via POP3/Outlook
- **Data Extraction**: Parse attachments for key metrics (MO, LOT, yield rates)
- **Report Generation**: Auto-generate Excel summary reports
- **File Management**: Handle CSV, Excel, compressed files with vendor-specific formats
- **Vendor Integration**: Support multiple semiconductor test vendors

## Supported Vendors & Keywords
- **ETD**: `anf` keyword
- **GTK**: `ft hold`, `ft lot` keywords  
- **JCET**: `jcet` keyword
- **LINGSEN**: `lingsen` keyword
- **XAHT**: `tianshui`, `西安` keywords
- **LLM**: Dedicated parser

## Key Domain Concepts
- **MO (Manufacturing Order)**: Primary business identifier
- **LOT**: Test batch identifier
- **Yield Rate**: Critical quality metric
- **Vendor Parser**: Factory pattern for different data formats
- **Email Classification**: Automatic vendor routing based on keywords

## Architecture Patterns
- **Hexagonal Architecture**: Clear separation between domain, application, and infrastructure
- **Factory Pattern**: Vendor-specific parsers
- **Strategy Pattern**: Different processing strategies per vendor
- **Repository Pattern**: Data persistence abstraction
- **Dependency Injection**: Constructor-based DI throughout

## Service Endpoints
- **Flask (Port 5000)**: Email inbox management service
- **FastAPI (Port 8010)**: FT-EQC processing service with `/ui` and `/docs`

## Data Flow
1. Email monitoring → Vendor classification → Parser selection → Data extraction → Report generation → File storage

## Business Rules
- All vendor data must include MO and LOT identifiers
- Yield rates must be validated against business thresholds
- Failed parsing attempts require manual review
- Reports generated in standardized Excel format
- File retention follows compliance requirements

## Error Handling Patterns
- Graceful degradation for parsing failures
- Comprehensive logging for audit trails
- Retry mechanisms for transient failures
- Dead letter queues for unprocessable emails