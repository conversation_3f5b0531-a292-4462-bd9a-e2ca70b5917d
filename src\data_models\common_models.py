"""通用的資料傳輸物件 (DTOs)"""

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ResponseStatus(str, Enum):
    """回應狀態"""
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ErrorType(str, Enum):
    """錯誤類型"""
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "authentication_error"
    AUTHORIZATION_ERROR = "authorization_error"
    NOT_FOUND_ERROR = "not_found_error"
    NETWORK_ERROR = "network_error"
    FILE_ERROR = "file_error"
    PROCESSING_ERROR = "processing_error"
    TIMEOUT_ERROR = "timeout_error"
    INTERNAL_ERROR = "internal_error"


class ErrorDetail(BaseModel):
    """錯誤詳情模型"""
    code: str = Field(..., description="錯誤代碼")
    message: str = Field(..., description="錯誤訊息")
    field: Optional[str] = Field(None, description="相關欄位")
    context: Optional[Dict[str, Any]] = Field(None, description="錯誤上下文")


class BaseResponse(BaseModel):
    """基礎回應模型"""
    status: ResponseStatus
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ErrorResponse(BaseResponse):
    """錯誤回應模型"""
    status: ResponseStatus = ResponseStatus.ERROR
    error_type: ErrorType
    error_code: str
    error_message: str
    details: Optional[List[ErrorDetail]] = None
    trace_id: Optional[str] = None


class SuccessResponse(BaseResponse):
    """成功回應模型"""
    status: ResponseStatus = ResponseStatus.SUCCESS
    data: Optional[Dict[str, Any]] = None


class PaginationInfo(BaseModel):
    """分頁資訊模型"""
    page: int = Field(1, description="當前頁碼", ge=1)
    page_size: int = Field(20, description="每頁項目數", ge=1, le=1000)
    total_items: int = Field(0, description="總項目數", ge=0)
    total_pages: int = Field(0, description="總頁數", ge=0)
    has_next: bool = Field(False, description="是否有下一頁")
    has_previous: bool = Field(False, description="是否有上一頁")


class PaginatedResponse(BaseResponse):
    """分頁回應模型"""
    data: List[Dict[str, Any]] = []
    pagination: PaginationInfo


class HealthCheckResponse(BaseModel):
    """健康檢查回應模型"""
    status: str
    version: str
    uptime: float
    services: Dict[str, str] = {}
    dependencies: Dict[str, str] = {}
    system_info: Dict[str, Any] = {}
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class MetricsResponse(BaseModel):
    """指標回應模型"""
    status: str
    metrics: Dict[str, Union[int, float, str]]
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ConfigurationResponse(BaseModel):
    """配置回應模型"""
    status: str
    config: Dict[str, Any]
    environment: str
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ValidationError(BaseModel):
    """驗證錯誤模型"""
    field: str
    message: str
    value: Optional[Any] = None


class ValidationErrorResponse(ErrorResponse):
    """驗證錯誤回應模型"""
    error_type: ErrorType = ErrorType.VALIDATION_ERROR
    validation_errors: List[ValidationError] = []


class TaskProgressInfo(BaseModel):
    """任務進度資訊模型"""
    task_id: str
    progress: float = Field(0.0, description="進度百分比 (0-100)", ge=0, le=100)
    status: str
    current_step: Optional[str] = None
    estimated_remaining: Optional[float] = None
    start_time: str
    last_update: str = Field(default_factory=lambda: datetime.now().isoformat())


class BulkOperationRequest(BaseModel):
    """批次操作請求模型"""
    operation: str = Field(..., description="操作類型")
    items: List[Dict[str, Any]] = Field(..., description="操作項目列表", min_items=1)
    options: Optional[Dict[str, Any]] = Field(None, description="操作選項")


class BulkOperationResponse(BaseModel):
    """批次操作回應模型"""
    status: str
    total_items: int
    successful_items: int
    failed_items: int
    results: List[Dict[str, Any]] = []
    errors: List[ErrorDetail] = []
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class FileOperationRequest(BaseModel):
    """檔案操作請求模型"""
    operation: str = Field(..., description="操作類型 (copy, move, delete, etc.)")
    source_paths: List[str] = Field(..., description="來源路徑列表", min_items=1)
    target_path: Optional[str] = Field(None, description="目標路徑")
    options: Optional[Dict[str, Any]] = Field(None, description="操作選項")


class FileOperationResponse(BaseModel):
    """檔案操作回應模型"""
    status: str
    operation: str
    processed_files: List[str] = []
    failed_files: List[str] = []
    total_files: int
    processing_time: float
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class SystemResourceInfo(BaseModel):
    """系統資源資訊模型"""
    cpu_usage: float = Field(0.0, description="CPU使用率 (%)", ge=0, le=100)
    memory_usage: float = Field(0.0, description="記憶體使用率 (%)", ge=0, le=100)
    disk_usage: float = Field(0.0, description="磁碟使用率 (%)", ge=0, le=100)
    network_io: Dict[str, int] = {}
    active_connections: int = 0
    uptime: float = 0.0


class LogEntry(BaseModel):
    """日誌條目模型"""
    timestamp: str
    level: str
    logger: str
    message: str
    context: Optional[Dict[str, Any]] = None
    trace_id: Optional[str] = None


class LogQueryRequest(BaseModel):
    """日誌查詢請求模型"""
    level: Optional[str] = Field(None, description="日誌級別")
    logger: Optional[str] = Field(None, description="記錄器名稱")
    start_time: Optional[datetime] = Field(None, description="開始時間")
    end_time: Optional[datetime] = Field(None, description="結束時間")
    search_text: Optional[str] = Field(None, description="搜尋文字")
    limit: int = Field(100, description="返回條目數限制", ge=1, le=10000)


class LogQueryResponse(BaseModel):
    """日誌查詢回應模型"""
    status: str
    logs: List[LogEntry]
    total_count: int
    query_time: float
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())