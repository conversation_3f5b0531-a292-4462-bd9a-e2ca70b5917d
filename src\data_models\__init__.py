"""
數據模型模組
包含郵件處理、檔案搜尋、網路操作等相關的資料傳輸物件
"""

# 郵件相關模型
from .email_models import (
    EmailData,
    EmailAttachment,
    VendorIdentificationResult,
    EmailParsingResult,
    ProcessingStatus,
    EmailMetadata,
    TaskData,
    FileProcessingInfo,
    EmailProcessingContext
)

# 搜尋相關模型
from .search_models import (
    TimeRangeType,
    ProductSearchRequest,
    FileInfoDTO,
    ProductSearchResponse,
    SearchTaskStatus,
    SearchTaskResponse,
    SearchProgressUpdate,
    SmartSearchRequest,
    SmartSearchResponse
)

# 處理相關模型
from .processing_models import (
    ProcessingToolType,
    ProcessingTaskStatus,
    ProcessingRequest,
    ProcessingStagingRequest,
    ProcessingTaskResponse,
    ProcessingResultResponse,
    ProcessingProgressUpdate,
    StagingTaskRequest,
    StagingTaskResponse,
    StagingResultResponse,
    TaskListResponse,
    SystemStatusResponse
)

# 網路相關模型
from .network_models import (
    NetworkConnectionStatus,
    NetworkCredentialsRequest,
    NetworkCredentialsResponse,
    NetworkConnectRequest,
    NetworkConnectResponse,
    NetworkPathValidateRequest,
    NetworkPathValidateResponse,
    NetworkFileInfo,
    NetworkFileListRequest,
    NetworkFileListResponse,
    NetworkDownloadRequest,
    NetworkDownloadResponse,
    NetworkUploadRequest,
    NetworkUploadResponse,
    NetworkConfigRequest,
    NetworkConfigResponse,
    NetworkHealthCheckResponse,
    CurrentUserResponse
)

# 通用模型
from .common_models import (
    ResponseStatus,
    ErrorType,
    ErrorDetail,
    BaseResponse,
    ErrorResponse,
    SuccessResponse,
    PaginationInfo,
    PaginatedResponse,
    HealthCheckResponse,
    MetricsResponse,
    ConfigurationResponse,
    ValidationError,
    ValidationErrorResponse,
    TaskProgressInfo,
    BulkOperationRequest,
    BulkOperationResponse,
    FileOperationRequest,
    FileOperationResponse,
    SystemResourceInfo,
    LogEntry,
    LogQueryRequest,
    LogQueryResponse
)

__all__ = [
    # 郵件相關
    "EmailData",
    "EmailAttachment", 
    "VendorIdentificationResult",
    "EmailParsingResult",
    "ProcessingStatus",
    "EmailMetadata",
    "TaskData",
    "FileProcessingInfo",
    "EmailProcessingContext",
    
    # 搜尋相關
    "TimeRangeType",
    "ProductSearchRequest",
    "FileInfoDTO", 
    "ProductSearchResponse",
    "SearchTaskStatus",
    "SearchTaskResponse",
    "SearchProgressUpdate",
    "SmartSearchRequest",
    "SmartSearchResponse",
    
    # 處理相關
    "ProcessingToolType",
    "ProcessingTaskStatus",
    "ProcessingRequest",
    "ProcessingStagingRequest",
    "ProcessingTaskResponse",
    "ProcessingResultResponse",
    "ProcessingProgressUpdate",
    "StagingTaskRequest",
    "StagingTaskResponse",
    "StagingResultResponse",
    "TaskListResponse",
    "SystemStatusResponse",
    
    # 網路相關
    "NetworkConnectionStatus",
    "NetworkCredentialsRequest",
    "NetworkCredentialsResponse",
    "NetworkConnectRequest",
    "NetworkConnectResponse",
    "NetworkPathValidateRequest",
    "NetworkPathValidateResponse",
    "NetworkFileInfo",
    "NetworkFileListRequest",
    "NetworkFileListResponse",
    "NetworkDownloadRequest",
    "NetworkDownloadResponse",
    "NetworkUploadRequest",
    "NetworkUploadResponse",
    "NetworkConfigRequest",
    "NetworkConfigResponse",
    "NetworkHealthCheckResponse",
    "CurrentUserResponse",
    
    # 通用模型
    "ResponseStatus",
    "ErrorType",
    "ErrorDetail",
    "BaseResponse",
    "ErrorResponse",
    "SuccessResponse",
    "PaginationInfo",
    "PaginatedResponse",
    "HealthCheckResponse",
    "MetricsResponse",
    "ConfigurationResponse",
    "ValidationError",
    "ValidationErrorResponse",
    "TaskProgressInfo",
    "BulkOperationRequest",
    "BulkOperationResponse",
    "FileOperationRequest",
    "FileOperationResponse",
    "SystemResourceInfo",
    "LogEntry",
    "LogQueryRequest",
    "LogQueryResponse"
]