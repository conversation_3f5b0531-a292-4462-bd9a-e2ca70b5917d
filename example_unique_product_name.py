"""唯一產品名稱功能示例"""

import tempfile
import asyncio
from pathlib import Path
from src.services.file_staging_service import FileStagingService


async def demonstrate_unique_product_name():
    """演示唯一產品名稱功能"""
    
    # 建立臨時目錄和檔案
    with tempfile.TemporaryDirectory() as temp_dir:
        # 建立測試檔案
        test_files = []
        for i in range(2):
            file_path = Path(temp_dir) / f"demo_file_{i}.csv"
            file_path.write_text(f"Product,Lot,Yield\nDEMO_PRODUCT,LOT{i},95.{i}")
            test_files.append(file_path)
        
        # 建立暫存服務
        staging_service = FileStagingService(
            base_staging_path="d:\\temp",
            verify_integrity=True
        )
        
        print("🎯 唯一產品名稱功能演示")
        print("=" * 50)
        
        # 示例1: 使用唯一產品名稱（預設）
        print("\n📁 示例1: 使用唯一產品名稱")
        product_name = "MY_PROJECT"
        
        task_id_1 = staging_service.create_staging_task(
            product_name=product_name,
            source_files=test_files,
            preserve_structure=True,
            use_unique_name=True  # 使用唯一名稱
        )
        
        task_1 = staging_service.get_task_status(task_id_1)
        print(f"原始產品名稱: {product_name}")
        print(f"唯一產品名稱: {task_1.staging_directory.name}")
        print(f"完整暫存路徑: {task_1.staging_directory}")
        
        # 示例2: 不使用唯一產品名稱
        print("\n📁 示例2: 不使用唯一產品名稱")
        
        task_id_2 = staging_service.create_staging_task(
            product_name=product_name,
            source_files=test_files,
            preserve_structure=True,
            use_unique_name=False  # 不使用唯一名稱
        )
        
        task_2 = staging_service.get_task_status(task_id_2)
        print(f"原始產品名稱: {product_name}")
        print(f"暫存目錄名稱: {task_2.staging_directory.name}")
        print(f"完整暫存路徑: {task_2.staging_directory}")
        
        # 示例3: 多個用戶使用相同產品名稱
        print("\n👥 示例3: 模擬多個用戶使用相同產品名稱")
        
        shared_product = "SHARED_PROJECT"
        unique_names = []
        
        for i in range(3):
            task_id = staging_service.create_staging_task(
                product_name=shared_product,
                source_files=test_files[:1],
                preserve_structure=True,
                use_unique_name=True
            )
            
            task = staging_service.get_task_status(task_id)
            unique_name = task.staging_directory.name
            unique_names.append(unique_name)
            
            print(f"用戶 {i+1}: {shared_product} -> {unique_name}")
            
            # 稍微延遲以確保時間戳不同
            await asyncio.sleep(1)
        
        # 驗證所有名稱都不同
        print(f"\n✅ 所有暫存目錄名稱都不同: {len(set(unique_names)) == len(unique_names)}")
        
        # 示例4: 特殊字符處理
        print("\n🔧 示例4: 特殊字符處理")
        
        special_names = [
            "Project@123!",
            "test-project",
            "My Project (v2)",
            "產品_測試"
        ]
        
        for special_name in special_names:
            unique_name = staging_service._generate_unique_product_name(special_name)
            print(f"{special_name} -> {unique_name}")
        
        print("\n🎉 演示完成！")
        print("\n💡 使用建議:")
        print("1. 預設啟用唯一產品名稱以避免衝突")
        print("2. 格式：[產品名稱]_[用戶名]_[日期]_[時間]")
        print("3. 自動清理特殊字符，確保檔案系統相容性")
        print("4. 支援多人同時使用相同產品名稱而不衝突")


if __name__ == "__main__":
    asyncio.run(demonstrate_unique_product_name())