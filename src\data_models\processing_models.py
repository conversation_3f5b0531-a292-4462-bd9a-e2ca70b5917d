"""檔案處理相關的資料傳輸物件 (DTOs)"""

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ProcessingToolType(str, Enum):
    """處理工具類型"""
    CSV_SUMMARY = "csv_summary"
    CODE_COMPARISON = "code_comparison"


class ProcessingTaskStatus(str, Enum):
    """處理任務狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    TIMEOUT = "timeout"


class ProcessingRequest(BaseModel):
    """檔案處理請求模型"""
    tool: ProcessingToolType = Field(..., description="處理工具類型")
    input_path: str = Field(..., description="輸入檔案或目錄路徑", min_length=1)
    use_staging: bool = Field(default=False, description="是否使用暫存功能")
    timeout: Optional[float] = Field(None, description="任務超時時間（秒）", gt=0)
    max_retries: int = Field(default=3, description="最大重試次數", ge=0, le=10)


class ProcessingStagingRequest(BaseModel):
    """帶暫存功能的檔案處理請求模型"""
    tool: ProcessingToolType = Field(..., description="處理工具類型")
    source_files: List[str] = Field(..., description="來源檔案列表", min_items=1)
    product_name: str = Field(..., description="產品名稱", min_length=1)
    preserve_structure: bool = Field(default=True, description="是否保持目錄結構")
    use_unique_name: bool = Field(default=True, description="是否使用唯一名稱")
    timeout: Optional[float] = Field(None, description="任務超時時間（秒）", gt=0)
    max_retries: int = Field(default=3, description="最大重試次數", ge=0, le=10)


class ProcessingTaskResponse(BaseModel):
    """處理任務回應模型"""
    task_id: str
    tool: ProcessingToolType
    input_path: str
    status: ProcessingTaskStatus
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: float = Field(0.0, description="進度百分比 (0-100)", ge=0, le=100)
    output_files: List[str] = []
    error_message: Optional[str] = None
    staging_task_id: Optional[str] = None
    staged_path: Optional[str] = None
    use_staging: bool = False
    retry_count: int = 0
    execution_time: float = 0.0


class ProcessingResultResponse(BaseModel):
    """處理結果回應模型"""
    success: bool
    task_id: str
    output_files: List[str]
    processing_time: float
    tool_used: str
    error_message: Optional[str] = None
    logs: List[str] = []
    retries_used: int = 0
    timeout_occurred: bool = False
    cancelled: bool = False
    rollback_performed: bool = False
    peak_cpu_usage: float = 0.0
    peak_memory_usage: int = 0
    average_speed: float = 0.0
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class ProcessingProgressUpdate(BaseModel):
    """處理進度更新模型"""
    task_id: str
    progress: float = Field(..., ge=0, le=100)
    status: ProcessingTaskStatus
    current_operation: Optional[str] = None
    files_processed: int = 0
    message: Optional[str] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class StagingTaskRequest(BaseModel):
    """暫存任務請求模型"""
    product_name: str = Field(..., description="產品名稱", min_length=1)
    source_files: List[str] = Field(..., description="來源檔案列表", min_items=1)
    preserve_structure: bool = Field(default=True, description="是否保持目錄結構")
    verify_integrity: bool = Field(default=True, description="是否驗證檔案完整性")
    use_unique_name: bool = Field(default=True, description="是否使用唯一名稱")
    timeout: Optional[float] = Field(None, description="任務超時時間（秒）", gt=0)


class StagingTaskResponse(BaseModel):
    """暫存任務回應模型"""
    task_id: str
    product_name: str
    staging_directory: str
    status: str
    created_at: str
    started_at: Optional[str] = None
    completed_at: Optional[str] = None
    progress: float = Field(0.0, description="進度百分比 (0-100)", ge=0, le=100)
    total_files: int = 0
    total_size: int = 0
    copied_size: int = 0
    error_message: Optional[str] = None


class StagingResultResponse(BaseModel):
    """暫存結果回應模型"""
    success: bool
    task_id: str
    staging_directory: str
    staged_files: List[str]
    total_files: int
    total_size: int
    staging_duration: float
    error_message: Optional[str] = None
    failed_files: List[str] = []
    integrity_check_passed: bool = True
    retries_used: int = 0
    average_speed: float = 0.0
    peak_memory: int = 0
    cpu_usage: float = 0.0
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class TaskListResponse(BaseModel):
    """任務列表回應模型"""
    tasks: List[ProcessingTaskResponse]
    total_count: int
    active_count: int
    completed_count: int
    failed_count: int
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())


class SystemStatusResponse(BaseModel):
    """系統狀態回應模型"""
    status: str
    active_tasks: int
    max_concurrent_tasks: int
    memory_usage: Dict[str, Any] = {}
    disk_usage: Dict[str, Any] = {}
    service_health: Dict[str, str] = {}
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())