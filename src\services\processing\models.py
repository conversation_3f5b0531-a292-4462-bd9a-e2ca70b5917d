"""檔案處理服務 - 資料模型
包含所有與檔案處理相關的資料類別、列舉和例外類別
"""

import asyncio
from typing import List, Optional, Callable
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum


class ProcessingStatus(str, Enum):
    """處理狀態"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    RETRYING = "retrying"
    TIMEOUT = "timeout"


class ProcessingTool(str, Enum):
    """處理工具類型"""
    CSV_SUMMARY = "csv_summary"
    CODE_COMPARISON = "code_comparison"


class ProcessingError(Exception):
    """處理相關錯誤基類"""
    pass


class ProcessingTimeoutError(ProcessingError):
    """處理超時錯誤"""
    pass


class ProcessingRetryExhaustedError(ProcessingError):
    """重試次數耗盡錯誤"""
    pass


class ProcessingCancellationError(ProcessingError):
    """處理取消錯誤"""
    pass


class ToolExecutionError(ProcessingError):
    """工具執行錯誤"""
    pass


@dataclass
class RetryConfig:
    """重試配置"""
    max_retries: int = 3
    initial_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True  # 添加隨機抖動避免重試風暴


@dataclass
class ProcessingTask:
    """處理任務"""
    task_id: str
    tool: ProcessingTool
    input_path: str
    status: ProcessingStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    output_files: List[str] = field(default_factory=list)
    error_message: Optional[str] = None
    progress: float = 0.0
    staging_task_id: Optional[str] = None  # 關聯的暫存任務ID
    staged_path: Optional[str] = None  # 暫存後的路徑
    use_staging: bool = False  # 是否使用暫存功能
    
    # 錯誤處理和重試相關
    retry_count: int = 0
    retry_config: RetryConfig = field(default_factory=RetryConfig)
    last_retry_at: Optional[datetime] = None
    timeout: Optional[float] = None  # 任務超時時間（秒）
    cancelled: bool = False
    cancel_event: Optional[asyncio.Event] = field(default_factory=lambda: asyncio.Event())
    
    # 回滾相關
    rollback_actions: List[Callable] = field(default_factory=list)
    rollback_completed: bool = False
    
    # 監控資料
    process_id: Optional[int] = None
    cpu_usage: float = 0.0
    memory_usage: int = 0
    execution_time: float = 0.0


@dataclass
class ProcessingResult:
    """處理結果"""
    success: bool
    task_id: str
    output_files: List[str]
    processing_time: float
    tool_used: str
    error_message: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    
    # 錯誤處理相關
    retries_used: int = 0
    timeout_occurred: bool = False
    cancelled: bool = False
    rollback_performed: bool = False
    
    # 效能統計
    peak_cpu_usage: float = 0.0
    peak_memory_usage: int = 0
    average_speed: float = 0.0  # 處理速度（檔案/秒或MB/秒）
